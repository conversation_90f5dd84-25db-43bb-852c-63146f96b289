<?php

namespace App\Controllers;

use App\Models\ExerciseModel;
use App\Models\PositionsModel;
use App\Models\AppxApplicationDetailsModel;
use App\Models\DakoiiOrgModel;
use App\Models\UsersModel;

class AdminController extends BaseController
{
    public $session;
    protected $exerciseModel;
    protected $positionsModel;
    protected $applicationsModel;
    protected $organizationsModel;
    protected $usersModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();

        // Initialize models
        $this->exerciseModel = new ExerciseModel();
        $this->positionsModel = new PositionsModel();
        $this->applicationsModel = new AppxApplicationDetailsModel();
        $this->organizationsModel = new DakoiiOrgModel();
        $this->usersModel = new UsersModel();
    }

    public function admin_dashboard()
    {
        // Check if user is logged in and has appropriate role
        if (!$this->session->get('logged_in') || !in_array($this->session->get('role'), ['admin', 'supervisor', 'user'])) {
            return redirect()->to(base_url())->with('error', 'Access denied. Admin privileges required.');
        }

        // Get current organization ID from session
        $org_id = $this->session->get('org_id');

        try {
            // Get real statistics from database
            $activeJobs = $this->getActiveJobsCount($org_id);
            $totalApplications = $this->getTotalApplicationsCount($org_id);
            $shortlistedCount = $this->getShortlistedCount($org_id);
            $interviewsCount = $this->getInterviewsCount($org_id);

            // Get recent applications
            $recentApplications = $this->getRecentApplications($org_id, 5);

            // Get upcoming interviews (mock for now since interview table structure not defined)
            $upcomingInterviews = $this->getUpcomingInterviews($org_id, 5);

            // Get organization information
            $organizationInfo = $this->getOrganizationInfo($org_id);

            // Get recent exercises
            $recentExercises = $this->getRecentExercises($org_id, 3);

            // Get unacknowledged applications count
            $unacknowledgedCount = $this->getUnacknowledgedApplicationsCount($org_id);

            // Get notifications not sent count
            $notificationsNotSent = $this->getNotificationsNotSentCount($org_id);

            $data = [
                'title' => 'Admin Dashboard',
                'menu' => 'dashboard',
                'user_name' => $this->session->get('name'),
                'active_jobs' => $activeJobs,
                'total_applications' => $totalApplications,
                'shortlisted_count' => $shortlistedCount,
                'interviews_count' => $interviewsCount,
                'recent_applications' => $recentApplications,
                'upcoming_interviews' => $upcomingInterviews,
                'new_jobs_this_week' => $this->getNewJobsThisWeek($org_id),
                'new_applications_today' => $this->getNewApplicationsToday($org_id),
                'new_shortlisted_this_week' => $this->getNewShortlistedThisWeek($org_id),
                'organization_info' => $organizationInfo,
                'recent_exercises' => $recentExercises,
                'unacknowledged_applications_count' => $unacknowledgedCount,
                'notifications_not_sent' => $notificationsNotSent,
            ];

        } catch (\Exception $e) {
            log_message('error', 'Admin Dashboard error: ' . $e->getMessage());

            // Try to get organization info even in fallback scenario
            $organizationInfo = null;
            try {
                $organizationInfo = $this->getOrganizationInfo($org_id);
            } catch (\Exception $orgException) {
                log_message('error', 'Failed to get organization info in fallback: ' . $orgException->getMessage());
            }

            // Fallback to default values if database error occurs
            $data = [
                'title' => 'Admin Dashboard',
                'menu' => 'dashboard',
                'user_name' => $this->session->get('name'),
                'active_jobs' => 0,
                'total_applications' => 0,
                'shortlisted_count' => 0,
                'interviews_count' => 0,
                'recent_applications' => [],
                'upcoming_interviews' => [],
                'new_jobs_this_week' => 0,
                'new_applications_today' => 0,
                'new_shortlisted_this_week' => 0,
                'organization_info' => $organizationInfo,
                'unacknowledged_applications_count' => 0,
                'notifications_not_sent' => 0,
            ];

            session()->setFlashdata('error', 'Error loading dashboard data. Please try again.');
        }

        return view('admin/admin_dashboard', $data);
    }

    private function getActiveJobsCount($org_id)
    {
        // Count active positions for this organization
        return $this->positionsModel
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
            ->where('exercises.org_id', $org_id)
            ->where('exercises.status', 'published')
            ->where('(positions.status = 1 OR positions.status = "active")')
            ->countAllResults();
    }

    private function getTotalApplicationsCount($org_id)
    {
        // Count all applications for this organization
        return $this->applicationsModel
            ->where('org_id', $org_id)
            ->where('deleted_at IS NULL')
            ->countAllResults();
    }

    private function getShortlistedCount($org_id)
    {
        // Count shortlisted applications for this organization
        return $this->applicationsModel
            ->where('org_id', $org_id)
            ->where('application_status', 'shortlisted')
            ->where('deleted_at IS NULL')
            ->countAllResults();
    }

    private function getInterviewsCount($org_id)
    {
        // Count applications with interview status for this organization
        return $this->applicationsModel
            ->where('org_id', $org_id)
            ->whereIn('application_status', ['interview_scheduled', 'interviewed'])
            ->where('deleted_at IS NULL')
            ->countAllResults();
    }

    private function getRecentApplications($org_id, $limit = 5)
    {
        // Get recent applications with position and applicant details
        return $this->applicationsModel
            ->select('
                appx_application_details.*,
                positions.designation,
                positions.classification
            ')
            ->join('positions', 'appx_application_details.position_id = positions.id', 'left')
            ->where('appx_application_details.org_id', $org_id)
            ->where('appx_application_details.deleted_at IS NULL')
            ->orderBy('appx_application_details.created_at', 'DESC')
            ->limit($limit)
            ->findAll();
    }

    private function getUpcomingInterviews($org_id, $limit = 5)
    {
        // Mock upcoming interviews since interview scheduling table structure not defined
        // In a real implementation, this would query an interviews table
        return [
            [
                'candidate_name' => 'Jane Smith',
                'position' => 'UI/UX Designer',
                'interview_date' => date('Y-m-d H:i:s', strtotime('+2 days')),
                'interview_type' => 'Virtual'
            ],
            [
                'candidate_name' => 'Michael Johnson',
                'position' => 'Software Engineer',
                'interview_date' => date('Y-m-d H:i:s', strtotime('+3 days')),
                'interview_type' => 'In-Person'
            ]
        ];
    }

    private function getNewJobsThisWeek($org_id)
    {
        // Count new positions created this week
        $weekStart = date('Y-m-d', strtotime('monday this week'));
        return $this->positionsModel
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
            ->where('exercises.org_id', $org_id)
            ->where('positions.created_at >=', $weekStart)
            ->countAllResults();
    }

    private function getNewApplicationsToday($org_id)
    {
        // Count new applications received today
        $today = date('Y-m-d');
        return $this->applicationsModel
            ->where('org_id', $org_id)
            ->where('DATE(created_at)', $today)
            ->where('deleted_at IS NULL')
            ->countAllResults();
    }

    private function getNewShortlistedThisWeek($org_id)
    {
        // Count newly shortlisted applications this week
        $weekStart = date('Y-m-d', strtotime('monday this week'));
        return $this->applicationsModel
            ->where('org_id', $org_id)
            ->where('application_status', 'shortlisted')
            ->where('updated_at >=', $weekStart)
            ->where('deleted_at IS NULL')
            ->countAllResults();
    }

    private function getOrganizationInfo($org_id)
    {
        // Get organization details
        return $this->organizationsModel->find($org_id);
    }

    private function getRecentExercises($org_id, $limit = 3)
    {
        // Get recent exercises for this organization
        return $this->exerciseModel
            ->where('org_id', $org_id)
            ->where('deleted_at IS NULL')
            ->orderBy('created_at', 'DESC')
            ->limit($limit)
            ->findAll();
    }

    private function getUnacknowledgedApplicationsCount($org_id)
    {
        // Count unacknowledged applications for this organization
        return $this->applicationsModel
            ->where('org_id', $org_id)
            ->where('is_received', 0)
            ->groupStart()
                ->where('received_status IS NULL')
                ->orWhere('received_status', '')
            ->groupEnd()
            ->where('deleted_at IS NULL')
            ->countAllResults();
    }

    private function getNotificationsNotSentCount($org_id)
    {
        // Count applications that are shortlisted or eliminated but notifications not sent
        return $this->applicationsModel
            ->where('org_id', $org_id)
            ->whereIn('shortlist_status', ['shortlisted', 'eliminated'])
            ->where('shortlisting_notified_at IS NULL')
            ->where('deleted_at IS NULL')
            ->countAllResults();
    }
}