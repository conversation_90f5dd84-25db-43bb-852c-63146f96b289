<?php
/**
 * View file for Registration by Positions Report
 *
 * @var array $exercise Exercise details
 * @var array $positionGroups Position groups with positions and application counts
 * @var array $statistics Statistics
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/exercises') ?>">Reports</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/dashboard/' . $exercise['id']) ?>">Dashboard</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/application-register/' . $exercise['id']) ?>">Application Register</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        Registration by Positions
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h3 mb-1">Registration by Positions</h2>
                    <p class="text-muted mb-0"><?= esc($exercise['exercise_name']) ?></p>
                </div>
                <div>
                    <a href="<?= base_url('reports/application-register/' . $exercise['id']) ?>" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Application Register
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        Summary Statistics
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="border rounded p-3 h-100">
                                <i class="fas fa-layer-group fa-2x text-primary mb-2"></i>
                                <h4 class="text-primary mb-1"><?= $statistics['total_groups'] ?></h4>
                                <p class="mb-0 text-muted">Position Groups</p>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="border rounded p-3 h-100">
                                <i class="fas fa-briefcase fa-2x text-info mb-2"></i>
                                <h4 class="text-info mb-1"><?= $statistics['total_positions'] ?></h4>
                                <p class="mb-0 text-muted">Total Positions</p>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="border rounded p-3 h-100">
                                <i class="fas fa-file-alt fa-2x text-success mb-2"></i>
                                <h4 class="text-success mb-1"><?= $statistics['total_applications'] ?></h4>
                                <p class="mb-0 text-muted">Total Applications</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Positions Table -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-layer-group me-2"></i>
                        Registration by Positions (<?= $statistics['total_positions'] ?> Positions)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($positionGroups)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Positions Found</h5>
                            <p class="text-muted">There are no positions for this exercise.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Position Group</th>
                                        <th>Organization</th>
                                        <th>Position Reference</th>
                                        <th>Designation</th>
                                        <th class="text-center">Applications</th>
                                        <th class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($positionGroups as $groupId => $group): ?>
                                        <?php foreach ($group['positions'] as $position): ?>
                                            <tr>
                                                <td>
                                                    <span class="badge bg-info text-white">
                                                        <?= esc($group['group_name']) ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <small class="text-muted">
                                                        <i class="fas fa-building me-1"></i>
                                                        <?= esc($position['org_name']) ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    <span class="badge bg-secondary">
                                                        <?= esc($position['position_reference']) ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <strong><?= esc($position['designation']) ?></strong>
                                                </td>
                                                <td class="text-center">
                                                    <span class="badge bg-<?= $position['application_count'] > 0 ? 'success' : 'warning' ?> fs-6">
                                                        <?= $position['application_count'] ?>
                                                    </span>
                                                </td>
                                                <td class="text-center">
                                                    <?php if ($position['application_count'] > 0): ?>
                                                        <a href="<?= base_url('reports/application-register-position/' . $position['position_id']) ?>"
                                                           class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-eye me-1"></i>
                                                            View Applications
                                                        </a>
                                                    <?php else: ?>
                                                        <span class="text-muted">No applications</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Information Card -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-body">
                    <h6 class="card-title text-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Information
                    </h6>
                    <p class="card-text">
                        This report shows all positions grouped by their position groups for the exercise "<?= esc($exercise['exercise_name']) ?>". 
                        Each position group displays the positions within it along with the total number of applications received for each position.
                        Click "View Applications" to see detailed application information for positions that have received applications.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
