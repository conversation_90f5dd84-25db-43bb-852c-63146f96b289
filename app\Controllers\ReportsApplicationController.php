<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use App\Models\ExerciseModel;
use App\Models\PositionsModel;
use App\Models\AppxApplicationDetailsModel;

/**
 * ReportsApplicationController
 *
 * Controller for application-related reports including application register and pre-screening reports
 */
class ReportsApplicationController extends Controller
{
    protected $exerciseModel;
    protected $positionsModel;
    protected $applicationModel;

    public function __construct()
    {
        helper(['url', 'form']);
        $this->exerciseModel = new ExerciseModel();
        $this->positionsModel = new PositionsModel();
        $this->applicationModel = new AppxApplicationDetailsModel();
    }

    /**
     * [GET] Application Register Report
     * URI: /reports/application-register/{exerciseId}
     */
    public function applicationRegister($exerciseId)
    {
        try {
            // Get exercise data
            $exercise = $this->exerciseModel->find($exerciseId);
            if (!$exercise) {
                return redirect()->to('reports/exercises')
                                ->with('error', 'Exercise not found');
            }

            // Get all applications for this exercise with position details
            $applications = $this->applicationModel->select('
                    appx_application_details.*,
                    positions.position_reference,
                    positions.designation as position_title,
                    CONCAT(appx_application_details.first_name, " ", appx_application_details.last_name) as full_name
                ')
                ->join('positions', 'appx_application_details.position_id = positions.id', 'left')
                ->where('appx_application_details.exercise_id', $exerciseId)
                ->orderBy('appx_application_details.created_at', 'DESC')
                ->findAll();

            // Get all positions for this exercise
            $positions = $this->positionsModel->select('positions.*')
                ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
                ->where('positions_groups.exercise_id', $exerciseId)
                ->where('positions.deleted_at IS NULL')
                ->findAll();

            // Calculate statistics
            $totalApplications = count($applications);
            $totalApplicants = count(array_unique(array_column($applications, 'applicant_id')));
            $positionsWithApplications = 0;
            $positionsWithoutApplications = 0;

            foreach ($positions as $position) {
                $hasApplications = false;
                foreach ($applications as $application) {
                    if ($application['position_id'] == $position['id']) {
                        $hasApplications = true;
                        break;
                    }
                }
                if ($hasApplications) {
                    $positionsWithApplications++;
                } else {
                    $positionsWithoutApplications++;
                }
            }

            $statistics = [
                'total_applications' => $totalApplications,
                'total_applicants' => $totalApplicants,
                'total_positions_with_applications' => $positionsWithApplications,
                'total_positions_without_applications' => $positionsWithoutApplications,
                'total_positions' => count($positions)
            ];

            $data = [
                'title' => 'Application Register Report - ' . $exercise['exercise_name'],
                'menu' => 'reports',
                'exercise' => $exercise,
                'applications' => $applications,
                'statistics' => $statistics
            ];

            return view('application_reports/appx_reports_application_register', $data);
        } catch (\Exception $e) {
            log_message('error', 'Error fetching application register data: ' . $e->getMessage());
            return redirect()->to('reports/exercises')
                            ->with('error', 'Error loading application register');
        }
    }

    /**
     * [GET] Pre-Screening Report
     * URI: /reports/pre-screening/{exerciseId}
     */
    public function preScreening($exerciseId)
    {
        // Get exercise data
        $exercise = $this->exerciseModel->find($exerciseId);
        if (!$exercise) {
            return redirect()->to('reports/exercises')
                            ->with('error', 'Exercise not found');
        }

        // Get position reference filter from query parameter
        $positionReference = $this->request->getGet('position_reference');

        // Get pre-screened applications for this exercise with optional filter
        $preScreeningResults = $this->applicationModel->getPreScreenedApplicationsForReportWithFilter(
            $exerciseId,
            $exercise['org_id'],
            $positionReference
        );

        // Get available position references for filter dropdown
        $positionReferences = $this->applicationModel->getPositionReferencesForPreScreenedApplications(
            $exerciseId,
            $exercise['org_id']
        );

        $data = [
            'title' => 'Pre-Screening Report - ' . $exercise['exercise_name'],
            'menu' => 'reports',
            'exercise' => $exercise,
            'pre_screening_results' => $preScreeningResults,
            'position_references' => $positionReferences,
            'selected_position_reference' => $positionReference
        ];

        return view('application_reports/appx_reports_pre_screening', $data);
    }

    /**
     * [GET] Pre-Screening Detail Report
     * URI: /reports/pre-screening-detail/{applicationId}
     */
    public function preScreeningDetail($applicationId)
    {
        // Get application with full details including screened by username
        $application = $this->applicationModel->select('
                appx_application_details.*,
                exercises.exercise_name,
                exercises.gazzetted_no,
                exercises.gazzetted_date,
                exercises.advertisement_no,
                exercises.advertisement_date,
                exercises.is_internal,
                exercises.mode_of_advertisement,
                exercises.publish_date_from,
                exercises.publish_date_to,
                exercises.description,
                exercises.pre_screen_criteria,
                exercises.applicants_information,
                exercises.applicants_notice,
                exercises.status as exercise_status,
                dakoii_org.org_name,
                dakoii_org.org_code,
                users.username as screened_by_username
            ')
            ->join('exercises', 'appx_application_details.exercise_id = exercises.id', 'left')
            ->join('dakoii_org', 'appx_application_details.org_id = dakoii_org.id', 'left')
            ->join('users', 'appx_application_details.pre_screened_by = users.id', 'left')
            ->where('appx_application_details.id', $applicationId)
            ->first();

        if (!$application) {
            return redirect()->to('reports/exercises')
                            ->with('error', 'Application not found');
        }

        $data = [
            'title' => 'Pre-Screening Detail - ' . $application['first_name'] . ' ' . $application['last_name'],
            'menu' => 'reports',
            'application' => $application
        ];

        return view('application_reports/appx_reports_pre_screening_detail', $data);
    }

    /**
     * [GET] Application Register Report by Position
     * URI: /reports/application-register-position/{positionId}
     */
    public function applicationRegisterByPosition($positionId)
    {
        try {
            // Get position data with exercise and organization details
            $position = $this->positionsModel->select('
                    positions.*,
                    positions_groups.group_name,
                    positions_groups.exercise_id,
                    exercises.exercise_name,
                    exercises.advertisement_no,
                    dakoii_org.org_name
                ')
                ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
                ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
                ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
                ->where('positions.id', $positionId)
                ->first();

            if (!$position) {
                return redirect()->to('reports/exercises')
                                ->with('error', 'Position not found');
            }

            // Get exercise data
            $exercise = $this->exerciseModel->find($position['exercise_id']);
            if (!$exercise) {
                return redirect()->to('reports/exercises')
                                ->with('error', 'Exercise not found');
            }

            // Get all applications for this specific position
            $applications = $this->applicationModel->select('
                    appx_application_details.*,
                    positions.position_reference,
                    positions.designation as position_title,
                    CONCAT(appx_application_details.first_name, " ", appx_application_details.last_name) as full_name
                ')
                ->join('positions', 'appx_application_details.position_id = positions.id', 'left')
                ->where('appx_application_details.position_id', $positionId)
                ->orderBy('appx_application_details.created_at', 'DESC')
                ->findAll();

            // Calculate statistics
            $totalApplications = count($applications);
            $totalApplicants = count(array_unique(array_column($applications, 'applicant_id')));

            $statistics = [
                'total_applications' => $totalApplications,
                'total_applicants' => $totalApplicants
            ];

            $data = [
                'title' => 'Application Register - ' . $position['designation'],
                'menu' => 'reports',
                'exercise' => $exercise,
                'position' => $position,
                'applications' => $applications,
                'statistics' => $statistics
            ];

            return view('application_reports/appx_reports_application_register_position', $data);
        } catch (\Exception $e) {
            log_message('error', 'Error fetching position application register data: ' . $e->getMessage());
            return redirect()->to('reports/exercises')
                            ->with('error', 'Error loading position application register');
        }
    }

    /**
     * [GET] Positions with Applications Report
     * URI: /reports/positions-with-applications/{exerciseId}
     */
    public function positionsWithApplications($exerciseId)
    {
        try {
            // Get exercise data
            $exercise = $this->exerciseModel->find($exerciseId);
            if (!$exercise) {
                return redirect()->to('reports/exercises')
                                ->with('error', 'Exercise not found');
            }

            // Get all positions for this exercise
            $positions = $this->positionsModel->select('
                    positions.*,
                    positions_groups.group_name,
                    dakoii_org.org_name,
                    COUNT(appx_application_details.id) as application_count
                ')
                ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
                ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
                ->join('appx_application_details', 'positions.id = appx_application_details.position_id', 'left')
                ->where('positions_groups.exercise_id', $exerciseId)
                ->where('(positions.status = 1 OR positions.status = "active")')
                ->groupBy('positions.id')
                ->having('application_count > 0')
                ->orderBy('positions.designation', 'ASC')
                ->findAll();

            $data = [
                'title' => 'Positions with Applications - ' . $exercise['exercise_name'],
                'menu' => 'reports',
                'exercise' => $exercise,
                'positions' => $positions
            ];

            return view('application_reports/appx_reports_positions_with_applications', $data);
        } catch (\Exception $e) {
            log_message('error', 'Error fetching positions with applications: ' . $e->getMessage());
            return redirect()->to('reports/exercises')
                            ->with('error', 'Error loading positions with applications');
        }
    }

    /**
     * [GET] Positions without Applications Report
     * URI: /reports/positions-without-applications/{exerciseId}
     */
    public function positionsWithoutApplications($exerciseId)
    {
        try {
            // Get exercise data
            $exercise = $this->exerciseModel->find($exerciseId);
            if (!$exercise) {
                return redirect()->to('reports/exercises')
                                ->with('error', 'Exercise not found');
            }

            // Get all positions for this exercise that have no applications
            $positions = $this->positionsModel->select('
                    positions.*,
                    positions_groups.group_name,
                    dakoii_org.org_name,
                    COUNT(appx_application_details.id) as application_count
                ')
                ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
                ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
                ->join('appx_application_details', 'positions.id = appx_application_details.position_id', 'left')
                ->where('positions_groups.exercise_id', $exerciseId)
                ->where('(positions.status = 1 OR positions.status = "active")')
                ->groupBy('positions.id')
                ->having('application_count = 0')
                ->orderBy('positions.designation', 'ASC')
                ->findAll();

            $data = [
                'title' => 'Positions without Applications - ' . $exercise['exercise_name'],
                'menu' => 'reports',
                'exercise' => $exercise,
                'positions' => $positions
            ];

            return view('application_reports/appx_reports_positions_without_applications', $data);
        } catch (\Exception $e) {
            log_message('error', 'Error fetching positions without applications: ' . $e->getMessage());
            return redirect()->to('reports/exercises')
                            ->with('error', 'Error loading positions without applications');
        }
    }

    /**
     * [GET] Registration by Positions Report
     * URI: /reports/registration-by-positions/{exerciseId}
     */
    public function registrationByPositions($exerciseId)
    {
        try {
            // Get exercise data
            $exercise = $this->exerciseModel->find($exerciseId);
            if (!$exercise) {
                return redirect()->to('reports/exercises')
                                ->with('error', 'Exercise not found');
            }

            // Get positions grouped by position groups with application counts
            $positionGroups = $this->positionsModel->select('
                    positions_groups.id as group_id,
                    positions_groups.group_name,
                    positions.id as position_id,
                    positions.designation,
                    positions.position_reference,
                    dakoii_org.org_name,
                    COUNT(appx_application_details.id) as application_count
                ')
                ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
                ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
                ->join('appx_application_details', 'positions.id = appx_application_details.position_id', 'left')
                ->where('positions_groups.exercise_id', $exerciseId)
                ->where('(positions.status = 1 OR positions.status = "active")')
                ->groupBy('positions.id')
                ->orderBy('positions_groups.group_name', 'ASC')
                ->orderBy('positions.designation', 'ASC')
                ->findAll();

            // Group positions by position group
            $groupedData = [];
            $totalApplications = 0;
            $totalPositions = 0;

            foreach ($positionGroups as $position) {
                $groupId = $position['group_id'];
                $groupName = $position['group_name'];

                if (!isset($groupedData[$groupId])) {
                    $groupedData[$groupId] = [
                        'group_name' => $groupName,
                        'positions' => [],
                        'total_applications' => 0
                    ];
                }

                $groupedData[$groupId]['positions'][] = $position;
                $groupedData[$groupId]['total_applications'] += $position['application_count'];
                $totalApplications += $position['application_count'];
                $totalPositions++;
            }

            $data = [
                'title' => 'Registration by Positions - ' . $exercise['exercise_name'],
                'menu' => 'reports',
                'exercise' => $exercise,
                'positionGroups' => $groupedData,
                'statistics' => [
                    'total_applications' => $totalApplications,
                    'total_positions' => $totalPositions,
                    'total_groups' => count($groupedData)
                ]
            ];

            return view('application_reports/appx_reports_registration_by_positions', $data);
        } catch (\Exception $e) {
            log_message('error', 'Error fetching registration by positions data: ' . $e->getMessage());
            return redirect()->to('reports/exercises')
                            ->with('error', 'Error loading registration by positions report');
        }
    }

    /**
     * [POST] Export Application Register Report
     * URI: /reports/application-register/export
     */
    public function exportApplicationRegister()
    {
        $exerciseId = $this->request->getPost('exercise_id');

        if (!$exerciseId) {
            log_message('error', 'PDF Export: Exercise ID not provided. POST data: ' . json_encode($this->request->getPost()));
            // Set JSON content type for error response
            $this->response->setContentType('application/json');
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Exercise ID is required'
            ]);
        }

        try {
            // Get exercise data with organization information
            $exercise = $this->exerciseModel->select('
                    exercises.*,
                    dakoii_org.org_name,
                    dakoii_org.org_code
                ')
                ->join('dakoii_org', 'exercises.org_id = dakoii_org.id', 'left')
                ->where('exercises.id', $exerciseId)
                ->first();

            if (!$exercise) {
                $this->response->setContentType('application/json');
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Exercise not found'
                ]);
            }

            // Get all applications for this exercise with position and organization details
            $applications = $this->applicationModel->select('
                    appx_application_details.*,
                    positions.position_reference,
                    positions.designation as position_title,
                    dakoii_org.org_name as application_org_name,
                    CONCAT(appx_application_details.first_name, " ", appx_application_details.last_name) as full_name
                ')
                ->join('positions', 'appx_application_details.position_id = positions.id', 'left')
                ->join('dakoii_org', 'appx_application_details.org_id = dakoii_org.id', 'left')
                ->where('appx_application_details.exercise_id', $exerciseId)
                ->orderBy('appx_application_details.created_at', 'DESC')
                ->findAll();

            log_message('info', 'PDF Export: Exercise data: ' . json_encode($exercise));
            log_message('info', 'PDF Export: Found ' . count($applications) . ' applications');

            // Generate and output PDF directly to browser
            $this->generateApplicationRegisterPDF($exercise, $applications);

        } catch (\Exception $e) {
            log_message('error', 'PDF Export Error: ' . $e->getMessage());
            // Set JSON content type for error response
            $this->response->setContentType('application/json');
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to generate PDF: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * [POST] Export Pre-Screening Report as PDF
     * URI: /reports/pre-screening/export
     */
    public function exportPreScreening()
    {
        $exerciseId = $this->request->getPost('exercise_id');
        $positionReference = $this->request->getPost('position_reference');

        if (!$exerciseId) {
            log_message('error', 'PDF Export: Exercise ID not provided. POST data: ' . json_encode($this->request->getPost()));
            // Set JSON content type for error response
            $this->response->setContentType('application/json');
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Exercise ID is required'
            ]);
        }

        try {
            // Get exercise data with organization info
            $exercise = $this->exerciseModel->select('
                    exercises.*,
                    dakoii_org.org_name,
                    dakoii_org.org_code
                ')
                ->join('dakoii_org', 'exercises.org_id = dakoii_org.id', 'left')
                ->where('exercises.id', $exerciseId)
                ->first();

            if (!$exercise) {
                $this->response->setContentType('application/json');
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Exercise not found'
                ]);
            }

            // Get pre-screened applications for this exercise with optional filter
            $preScreeningResults = $this->applicationModel->getPreScreenedApplicationsForReportWithFilter(
                $exerciseId,
                $exercise['org_id'],
                $positionReference
            );

            log_message('info', 'PDF Export: Exercise data: ' . json_encode($exercise));
            log_message('info', 'PDF Export: Position reference filter: ' . ($positionReference ?: 'None'));
            log_message('info', 'PDF Export: Found ' . count($preScreeningResults) . ' pre-screening results');

            // Generate and output PDF directly to browser
            $this->generatePreScreeningPDF($exercise, $preScreeningResults, $positionReference);

        } catch (\Exception $e) {
            log_message('error', 'PDF Export Error: ' . $e->getMessage());
            log_message('error', 'PDF Export Stack Trace: ' . $e->getTraceAsString());

            // Set JSON content type for error response
            $this->response->setContentType('application/json');
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to generate PDF: ' . $e->getMessage()
            ]);
        }
    }



    /**
     * Generate Pre-Screening Report PDF
     */
    private function generatePreScreeningPDF($exercise, $preScreeningResults, $positionReference = null)
    {
        log_message('info', 'PDF Generation: Starting PDF generation');

        try {
            // Create custom TCPDF class with PNG flag colored footer
            $pdf = new class('L', 'mm', 'A4', true, 'UTF-8', false) extends \TCPDF {
                public function Footer() {
                    // Position at 18 mm from bottom
                    $this->SetY(-18);

                    // Create PNG flag gradient footer background (Red, Black, Gold)
                    $footerY = $this->GetY();
                    $footerHeight = 15;
                    $footerWidth = 277;

                    // Draw red section (left third)
                    $this->SetFillColor(240, 15, 0); // PNG Red #F00F00
                    $this->Rect(10, $footerY, $footerWidth / 3, $footerHeight, 'F');

                    // Draw black section (middle third)
                    $this->SetFillColor(0, 0, 0); // PNG Black #000000
                    $this->Rect(10 + ($footerWidth / 3), $footerY, $footerWidth / 3, $footerHeight, 'F');

                    // Draw gold section (right third)
                    $this->SetFillColor(255, 194, 15); // PNG Gold #FFC20F
                    $this->Rect(10 + (2 * $footerWidth / 3), $footerY, $footerWidth / 3, $footerHeight, 'F');

                    // Add a subtle top border line
                    $this->SetDrawColor(255, 255, 255);
                    $this->Line(10, $footerY, 287, $footerY);

                    // Footer content with white text for better contrast
                    $this->SetFont('helvetica', '', 8);
                    $this->SetTextColor(255, 255, 255); // White text for contrast

                    // Move down a bit after the line
                    $this->SetY($footerY + 1);

                    // First row
                    // Left side - System info (on red background)
                    $this->SetX(12);
                    $this->Cell(90, 3, 'Generated by ' . SYSTEM_NAME . ' ' . SYSTEM_VERSION, 0, 0, 'L');

                    // Center - Organization info (on black background)
                    $this->SetX(100);
                    $this->Cell(87, 3, 'Dakoii Echad Recruitment & Selection System', 0, 0, 'C');

                    // Right side - Date and page (on gold background, use black text for better contrast)
                    $this->SetTextColor(0, 0, 0); // Black text on gold background
                    $this->SetX(185);
                    $this->Cell(90, 3, 'Generated on: ' . date('M d, Y H:i') . ' | Page ' . $this->getAliasNumPage() . ' of ' . $this->getAliasNbPages(), 0, 0, 'R');

                    // Second row
                    $this->SetY($this->GetY() + 3);
                    $this->SetTextColor(255, 255, 255); // White text for left section

                    // Left side - AI-Powered (on red background)
                    $this->SetX(12);
                    $this->Cell(90, 3, 'AI-Powered', 0, 0, 'L');

                    // Center - Development credits (on black background)
                    $this->SetX(100);
                    $this->Cell(87, 3, 'Developed by Dakoii Systems & Echad Consultancy Services', 0, 0, 'C');

                    // Third row - Website centered across all sections
                    $this->SetY($this->GetY() + 3);
                    $this->SetTextColor(255, 255, 255); // White text
                    $this->SetX(10);
                    $this->Cell(277, 3, 'ders.dakoiims.com', 0, 0, 'C');

                    // Reset text color
                    $this->SetTextColor(0, 0, 0);
                }
            };

            // Set document information
            $pdf->SetCreator('DERS - Dakoii Echad Recruitment & Selection System');
            $pdf->SetAuthor(SYSTEM_NAME);
            $pdf->SetTitle('Pre-Screening Report - ' . $exercise['exercise_name']);
            $pdf->SetSubject('Pre-Screening Report');

            // Set margins
            $pdf->SetMargins(10, 20, 10);
            $pdf->SetHeaderMargin(5);
            $pdf->SetFooterMargin(20);

            // Set auto page breaks
            $pdf->SetAutoPageBreak(true, 25);

            // Remove default header but keep custom footer
            $pdf->setPrintHeader(false);
            $pdf->setPrintFooter(true);

            // Set font
            $pdf->SetFont('helvetica', '', 10);

            $pdf->AddPage();

            // Add organization logo at the top
            $logoPath = FCPATH . 'public/assets/system_img/system-logo.png';
            if (file_exists($logoPath)) {
                // Center the logo
                $logoWidth = 20; // 20mm width
                $logoHeight = 20; // 20mm height
                $pageWidth = $pdf->getPageWidth();
                $logoX = ($pageWidth - $logoWidth) / 2;

                $pdf->Image($logoPath, $logoX, $pdf->GetY(), $logoWidth, $logoHeight, 'PNG', '', 'T', false, 300, '', false, false, 0, false, false, false);
                $pdf->Ln($logoHeight + 5); // Move down after logo
            }

            // Title
            $pdf->SetFont('helvetica', 'B', 16);
            $pdf->Cell(0, 10, 'Pre-Screening Report', 0, 1, 'C');
            $pdf->Ln(5);

            // Exercise Information
            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->Cell(0, 8, 'Exercise Information', 0, 1);
            $pdf->SetFont('helvetica', '', 10);
            $pdf->Cell(40, 6, 'Exercise Name:', 0, 0);
            $pdf->Cell(0, 6, $exercise['exercise_name'] ?? 'N/A', 0, 1);
            $pdf->Cell(40, 6, 'Advertisement No:', 0, 0);
            $pdf->Cell(0, 6, $exercise['advertisement_no'] ?? 'N/A', 0, 1);
            $pdf->Cell(40, 6, 'Organization:', 0, 0);
            $pdf->Cell(0, 6, $exercise['org_name'] ?? 'N/A', 0, 1);

            // Add position filter information if applied
            if (!empty($positionReference)) {
                $pdf->Cell(40, 6, 'Position Filter:', 0, 0);
                $pdf->Cell(0, 6, $positionReference, 0, 1);
            }

            $pdf->Cell(40, 6, 'Generated On:', 0, 0);
            $pdf->Cell(0, 6, date('M d, Y H:i'), 0, 1);
            $pdf->Ln(10);

            // Summary Statistics
            $totalApplications = count($preScreeningResults);
            $passedCount = 0;
            $failedCount = 0;

            foreach ($preScreeningResults as $result) {
                if ($result['pre_screened_status'] === 'passed') {
                    $passedCount++;
                } else {
                    $failedCount++;
                }
            }

            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->Cell(0, 8, 'Summary Statistics', 0, 1);
            $pdf->SetFont('helvetica', '', 10);
            $pdf->Cell(40, 6, 'Total Applications:', 0, 0);
            $pdf->Cell(0, 6, $totalApplications, 0, 1);
            $pdf->Cell(40, 6, 'Passed:', 0, 0);
            $pdf->Cell(0, 6, $passedCount, 0, 1);
            $pdf->Cell(40, 6, 'Failed:', 0, 0);
            $pdf->Cell(0, 6, $failedCount, 0, 1);
            $pdf->Ln(10);

            // Applications Table Header
            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->Cell(0, 8, 'Pre-Screening Results', 0, 1);

            // Define column widths for better layout
            $colWidths = [8, 30, 25, 18, 35, 12, 18, 25, 97];
            $headers = ['#', 'Applicant Name', 'Application No', 'Position Ref', 'Position Title', 'Gender', 'Status', 'Screened Date', 'Criteria Results Details'];

            // Table headers
            $pdf->SetFont('helvetica', 'B', 8);
            for ($i = 0; $i < count($headers); $i++) {
                $pdf->Cell($colWidths[$i], 8, $headers[$i], 1, 0, 'C');
            }
            $pdf->Ln();

            // Table data
            $pdf->SetFont('helvetica', '', 8);
            $rowNum = 1;

            foreach ($preScreeningResults as $result) {
                // Parse criteria results for detailed display
                $criteriaResultsDetails = 'N/A';
                if (!empty($result['pre_screened_criteria_results'])) {
                    $criteriaData = json_decode($result['pre_screened_criteria_results'], true);
                    if ($criteriaData && isset($criteriaData['criteria_evaluations'])) {
                        $detailsArray = [];
                        foreach ($criteriaData['criteria_evaluations'] as $evaluation) {
                            $criteriaName = isset($evaluation['criteria_name']) ? $evaluation['criteria_name'] : 'Unknown';
                            $evaluationResult = isset($evaluation['evaluation_result']) ? strtoupper($evaluation['evaluation_result']) : 'N/A';
                            $detailsArray[] = $criteriaName . ': ' . $evaluationResult;
                        }
                        $criteriaResultsDetails = implode("\n", $detailsArray);
                    }
                }

                // Prepare row data
                $rowData = [
                    $rowNum,
                    $result['full_name'],
                    $result['application_number'],
                    $result['position_reference'] ?? 'N/A',
                    $result['position_title'] ?? 'N/A',
                    substr($result['gender'], 0, 1),
                    ucfirst($result['pre_screened_status']),
                    date('M d, Y', strtotime($result['pre_screened_at'])),
                    $criteriaResultsDetails
                ];

                // Calculate required height for each column
                $pdf->SetFont('helvetica', '', 8);
                $maxHeight = 8; // Minimum row height

                for ($i = 0; $i < count($rowData); $i++) {
                    $cellText = $rowData[$i];
                    $cellWidth = $colWidths[$i] - 2; // Account for padding

                    // Calculate how many lines this text will need
                    $lines = $pdf->getNumLines($cellText, $cellWidth);
                    $requiredHeight = $lines * 4 + 2; // 4mm per line + 2mm padding

                    if ($requiredHeight > $maxHeight) {
                        $maxHeight = $requiredHeight;
                    }
                }

                $rowHeight = $maxHeight;

                // Check if this row will fit on current page
                $currentY = $pdf->GetY();
                $pageHeight = $pdf->getPageHeight();
                $bottomMargin = $pdf->getBreakMargin();

                if (($currentY + $rowHeight) > ($pageHeight - $bottomMargin)) {
                    // Move to new page
                    $pdf->AddPage();
                    // Recreate table header on new page
                    $pdf->SetFont('helvetica', 'B', 8);
                    for ($i = 0; $i < count($headers); $i++) {
                        $pdf->Cell($colWidths[$i], 8, $headers[$i], 1, 0, 'C');
                    }
                    $pdf->Ln();
                    $pdf->SetFont('helvetica', '', 8);
                }

                // Store current position for row drawing
                $startY = $pdf->GetY();
                $startX = $pdf->GetX();

                // Draw all cells with proper text wrapping and consistent height
                $pdf->SetFont('helvetica', '', 8);

                // Draw each cell with proper text wrapping
                $currentX = $startX;
                for ($i = 0; $i < count($rowData); $i++) {
                    $cellText = $rowData[$i];
                    $cellWidth = $colWidths[$i];
                    $align = ($i == 0 || $i == 5 || $i == 6) ? 'C' : 'L'; // Center for #, Gender, Status

                    // Draw border for all cells
                    $pdf->Rect($currentX, $startY, $cellWidth, $rowHeight);

                    // Set position with small padding
                    $pdf->SetXY($currentX + 1, $startY + 1);

                    // Use MultiCell for all cells to handle text wrapping properly
                    $pdf->MultiCell($cellWidth - 2, 4, $cellText, 0, $align, false, 0, '', '', true, 0, false, true, $rowHeight - 2, 'M');

                    $currentX += $cellWidth;
                }

                // Move to next row
                $pdf->SetY($startY + $rowHeight);

                $rowNum++;
            }

            // Generate filename for download
            $filename = 'pre_screening_report_' . str_replace(' ', '_', $exercise['exercise_name']) . '_' . date('Y-m-d_H-i-s') . '.pdf';

            log_message('info', 'PDF Generation: Outputting PDF directly to browser: ' . $filename);

            // Output PDF directly to browser for download
            $pdf->Output($filename, 'D');

        } catch (\Exception $e) {
            log_message('error', 'PDF Generation Error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Generate Application Register Report PDF
     */
    private function generateApplicationRegisterPDF($exercise, $applications)
    {
        log_message('info', 'PDF Generation: Starting Application Register PDF generation');

        try {
            // Create custom TCPDF class with PNG flag colored footer
            $pdf = new class('L', 'mm', 'A4', true, 'UTF-8', false) extends \TCPDF {
                public function Footer() {
                    // Position at 18 mm from bottom
                    $this->SetY(-18);

                    // Create PNG flag gradient footer background (Red, Black, Gold)
                    $footerY = $this->GetY();
                    $footerHeight = 15;
                    $footerWidth = 277;

                    // Draw red section (left third)
                    $this->SetFillColor(240, 15, 0); // PNG Red #F00F00
                    $this->Rect(10, $footerY, $footerWidth / 3, $footerHeight, 'F');

                    // Draw black section (middle third)
                    $this->SetFillColor(0, 0, 0); // PNG Black #000000
                    $this->Rect(10 + ($footerWidth / 3), $footerY, $footerWidth / 3, $footerHeight, 'F');

                    // Draw gold section (right third)
                    $this->SetFillColor(255, 194, 15); // PNG Gold #FFC20F
                    $this->Rect(10 + (2 * $footerWidth / 3), $footerY, $footerWidth / 3, $footerHeight, 'F');

                    // Add a subtle top border line
                    $this->SetDrawColor(255, 255, 255);
                    $this->Line(10, $footerY, 287, $footerY);

                    // Footer content with white text for better contrast
                    $this->SetFont('helvetica', '', 8);
                    $this->SetTextColor(255, 255, 255); // White text for contrast

                    // Move down a bit after the line
                    $this->SetY($footerY + 1);

                    // First row
                    // Left side - System info (on red background)
                    $this->SetX(12);
                    $this->Cell(90, 3, 'Generated by ' . SYSTEM_NAME . ' ' . SYSTEM_VERSION, 0, 0, 'L');

                    // Center - Organization info (on black background)
                    $this->SetX(100);
                    $this->Cell(87, 3, 'Dakoii Echad Recruitment & Selection System', 0, 0, 'C');

                    // Right side - Date and page (on gold background, use black text for better contrast)
                    $this->SetTextColor(0, 0, 0); // Black text on gold background
                    $this->SetX(185);
                    $this->Cell(90, 3, 'Generated on: ' . date('M d, Y H:i') . ' | Page ' . $this->getAliasNumPage() . ' of ' . $this->getAliasNbPages(), 0, 0, 'R');

                    // Second row
                    $this->SetY($this->GetY() + 3);
                    $this->SetTextColor(255, 255, 255); // White text for left section

                    // Left side - AI-Powered (on red background)
                    $this->SetX(12);
                    $this->Cell(90, 3, 'AI-Powered', 0, 0, 'L');

                    // Center - Development credits (on black background)
                    $this->SetX(100);
                    $this->Cell(87, 3, 'Developed by Dakoii Systems & Echad Consultancy Services', 0, 0, 'C');

                    // Third row - Website centered across all sections
                    $this->SetY($this->GetY() + 3);
                    $this->SetTextColor(255, 255, 255); // White text
                    $this->SetX(10);
                    $this->Cell(277, 3, 'ders.dakoiims.com', 0, 0, 'C');

                    // Reset text color
                    $this->SetTextColor(0, 0, 0);
                }
            };

            // Set document information
            $pdf->SetCreator('DERS - Dakoii Echad Recruitment & Selection System');
            $pdf->SetAuthor(SYSTEM_NAME);
            $pdf->SetTitle('Application Register Report - ' . $exercise['exercise_name']);
            $pdf->SetSubject('Application Register Report');

            // Set margins
            $pdf->SetMargins(10, 20, 10);
            $pdf->SetHeaderMargin(5);
            $pdf->SetFooterMargin(20);

            // Set auto page breaks
            $pdf->SetAutoPageBreak(true, 25);

            // Remove default header but keep custom footer
            $pdf->setPrintHeader(false);
            $pdf->setPrintFooter(true);

            // Set font
            $pdf->SetFont('helvetica', '', 10);

            $pdf->AddPage();

            // Add organization logo at the top
            $logoPath = FCPATH . 'public/assets/system_img/system-logo.png';
            if (file_exists($logoPath)) {
                // Center the logo
                $logoWidth = 20; // 20mm width
                $logoHeight = 20; // 20mm height
                $pageWidth = $pdf->getPageWidth();
                $logoX = ($pageWidth - $logoWidth) / 2;

                $pdf->Image($logoPath, $logoX, $pdf->GetY(), $logoWidth, $logoHeight, 'PNG', '', 'T', false, 300, '', false, false, 0, false, false, false);
                $pdf->Ln($logoHeight + 5); // Move down after logo
            }

            // Title
            $pdf->SetFont('helvetica', 'B', 16);
            $pdf->Cell(0, 10, 'Application Register Report', 0, 1, 'C');
            $pdf->Ln(5);

            // Exercise Information
            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->Cell(0, 8, 'Exercise Information', 0, 1);
            $pdf->SetFont('helvetica', '', 10);
            $pdf->Cell(40, 6, 'Exercise Name:', 0, 0);
            $pdf->Cell(0, 6, $exercise['exercise_name'] ?? 'N/A', 0, 1);
            $pdf->Cell(40, 6, 'Advertisement No:', 0, 0);
            $pdf->Cell(0, 6, $exercise['advertisement_no'] ?? 'N/A', 0, 1);
            $pdf->Cell(40, 6, 'Organization:', 0, 0);
            $pdf->Cell(0, 6, $exercise['org_name'] ?? 'N/A', 0, 1);
            $pdf->Cell(40, 6, 'Generated On:', 0, 0);
            $pdf->Cell(0, 6, date('M d, Y H:i'), 0, 1);
            $pdf->Ln(10);

            // Summary Statistics
            $totalApplications = count($applications);

            // Count unique applicants (not applications)
            $uniqueApplicants = [];
            $maleApplicants = [];
            $femaleApplicants = [];

            foreach ($applications as $application) {
                $applicantId = $application['applicant_id'];
                $gender = trim($application['gender'] ?? '');

                // Track unique applicants
                if (!in_array($applicantId, $uniqueApplicants)) {
                    $uniqueApplicants[] = $applicantId;

                    // Count gender for unique applicants only
                    if (strtolower($gender) === 'male') {
                        $maleApplicants[] = $applicantId;
                    } elseif (strtolower($gender) === 'female') {
                        $femaleApplicants[] = $applicantId;
                    }
                }
            }

            $totalApplicants = count($uniqueApplicants);
            $maleCount = count($maleApplicants);
            $femaleCount = count($femaleApplicants);

            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->Cell(0, 8, 'Summary Statistics', 0, 1);
            $pdf->SetFont('helvetica', '', 10);
            $pdf->Cell(40, 6, 'Total Applications:', 0, 0);
            $pdf->Cell(0, 6, $totalApplications, 0, 1);
            $pdf->Cell(40, 6, 'Total Applicants:', 0, 0);
            $pdf->Cell(0, 6, $totalApplicants, 0, 1);
            $pdf->Cell(40, 6, 'Male Applicants:', 0, 0);
            $pdf->Cell(0, 6, $maleCount, 0, 1);
            $pdf->Cell(40, 6, 'Female Applicants:', 0, 0);
            $pdf->Cell(0, 6, $femaleCount, 0, 1);
            $pdf->Ln(10);

            return $this->addApplicationRegisterContent($pdf, $applications, $exercise);

        } catch (\Exception $e) {
            log_message('error', 'PDF Generation Error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Add Application Register table content to PDF
     */
    private function addApplicationRegisterContent($pdf, $applications, $exercise)
    {
        // Applications Table Header
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->Cell(0, 8, 'Application Register', 0, 1);

        // Define column widths to fill full page width (277mm total width for landscape A4)
        $colWidths = [12, 42, 48, 18, 38, 28, 52, 32]; // Total: 270mm (fills page width)
        $headers = ['#', 'Application No', 'Applicant Name', 'Gender', 'Email', 'Position Ref', 'Position Title', 'Date Applied'];

        // Table headers
        $pdf->SetFont('helvetica', 'B', 8);
        for ($i = 0; $i < count($headers); $i++) {
            $pdf->Cell($colWidths[$i], 8, $headers[$i], 1, 0, 'C');
        }
        $pdf->Ln();

        // Table data
        $pdf->SetFont('helvetica', '', 8);
        $rowNum = 1;

        foreach ($applications as $application) {
            // Prepare row data (excluding Date of Birth, current employment status, place of origin, contact details)
            $rowData = [
                $rowNum,
                $application['application_number'] ?? 'N/A',
                $application['full_name'] ?? 'N/A',
                $application['gender'] ?? 'N/A',
                $application['email_address'] ?? 'N/A',
                $application['position_reference'] ?? 'N/A',
                $application['position_title'] ?? 'N/A',
                date('M d, Y', strtotime($application['created_at']))
            ];

            // Calculate required height for each column
            $pdf->SetFont('helvetica', '', 8);
            $maxHeight = 8; // Minimum row height

            for ($i = 0; $i < count($rowData); $i++) {
                $cellText = $rowData[$i];
                $cellWidth = $colWidths[$i] - 2; // Account for padding

                // Calculate how many lines this text will need
                $lines = $pdf->getNumLines($cellText, $cellWidth);
                $requiredHeight = $lines * 4 + 2; // 4mm per line + 2mm padding

                if ($requiredHeight > $maxHeight) {
                    $maxHeight = $requiredHeight;
                }
            }

            $rowHeight = $maxHeight;

            // Check if this row will fit on current page
            $currentY = $pdf->GetY();
            $pageHeight = $pdf->getPageHeight();
            $bottomMargin = $pdf->getBreakMargin();

            if (($currentY + $rowHeight) > ($pageHeight - $bottomMargin)) {
                // Move to new page
                $pdf->AddPage();
                // Recreate table header on new page
                $pdf->SetFont('helvetica', 'B', 8);
                for ($i = 0; $i < count($headers); $i++) {
                    $pdf->Cell($colWidths[$i], 8, $headers[$i], 1, 0, 'C');
                }
                $pdf->Ln();
                $pdf->SetFont('helvetica', '', 8);
            }

            // Store current position for row drawing
            $startY = $pdf->GetY();
            $startX = $pdf->GetX();

            // Draw all cells with proper text wrapping and consistent height
            $pdf->SetFont('helvetica', '', 8);

            // Draw each cell with proper text wrapping
            $currentX = $startX;
            for ($i = 0; $i < count($rowData); $i++) {
                $cellText = $rowData[$i];
                $cellWidth = $colWidths[$i];
                $align = ($i == 0 || $i == 3) ? 'C' : 'L'; // Center for #, Gender

                // Draw border for all cells
                $pdf->Rect($currentX, $startY, $cellWidth, $rowHeight);

                // Set position with small padding
                $pdf->SetXY($currentX + 1, $startY + 1);

                // Use MultiCell for all cells to handle text wrapping properly
                $pdf->MultiCell($cellWidth - 2, 4, $cellText, 0, $align, false, 0, '', '', true, 0, false, true, $rowHeight - 2, 'M');

                $currentX += $cellWidth;
            }

            // Move to next row
            $pdf->SetY($startY + $rowHeight);

            $rowNum++;
        }

        // Generate filename for download
        $filename = 'application_register_report_' . str_replace(' ', '_', $exercise['exercise_name'] ?? 'exercise') . '_' . date('Y-m-d_H-i-s') . '.pdf';

        log_message('info', 'PDF Generation: Outputting PDF directly to browser: ' . $filename);

        // Output PDF directly to browser for download
        $pdf->Output($filename, 'D');
    }

}
