<?php
/**
 * View file for Position-specific Application Register Report
 *
 * @var array $exercise Exercise details
 * @var array $position Position details
 * @var array $applications List of applications for this position
 * @var array $statistics Statistics
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/exercises') ?>">Reports</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/dashboard/' . $exercise['id']) ?>">Dashboard</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/application-register/' . $exercise['id']) ?>">Application Register</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?= esc($position['designation']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h3 mb-1">Application Register</h2>
                    <p class="text-muted mb-0"><?= esc($position['designation']) ?></p>
                    <small class="text-info">
                        <i class="fas fa-building me-1"></i><?= esc($position['org_name']) ?> | 
                        <i class="fas fa-layer-group me-1"></i><?= esc($position['group_name']) ?>
                    </small>
                </div>
                <div>
                    <button type="button" class="btn btn-success me-2" onclick="exportApplicationRegisterByPosition()">
                        <i class="fas fa-file-pdf me-1"></i>
                        Export PDF
                    </button>
                    <a href="<?= base_url('reports/positions-with-applications/' . $exercise['id']) ?>" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Positions
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Position Details Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-briefcase me-2"></i>
                        Position Details
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Position:</dt>
                                <dd class="col-sm-8"><?= esc($position['designation']) ?></dd>
                                <dt class="col-sm-4">Reference:</dt>
                                <dd class="col-sm-8"><?= esc($position['position_reference']) ?></dd>
                                <dt class="col-sm-4">Classification:</dt>
                                <dd class="col-sm-8"><?= esc($position['classification']) ?></dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Award:</dt>
                                <dd class="col-sm-8"><?= esc($position['award']) ?></dd>
                                <dt class="col-sm-4">Location:</dt>
                                <dd class="col-sm-8"><?= esc($position['location']) ?></dd>
                                <dt class="col-sm-4">Group:</dt>
                                <dd class="col-sm-8"><?= esc($position['group_name']) ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        Application Summary Statistics
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-6 mb-3">
                            <div class="border rounded p-3 h-100">
                                <i class="fas fa-file-alt fa-2x text-primary mb-2"></i>
                                <h4 class="text-primary mb-1"><?= $statistics['total_applications'] ?></h4>
                                <p class="mb-0 text-muted">Total Applications</p>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="border rounded p-3 h-100">
                                <i class="fas fa-users fa-2x text-success mb-2"></i>
                                <h4 class="text-success mb-1"><?= $statistics['total_applicants'] ?></h4>
                                <p class="mb-0 text-muted">Unique Applicants</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Applications Register -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-file-alt me-2"></i>
                        Application Register (<?= count($applications) ?> Applications)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($applications)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No applications found</h5>
                            <p class="text-muted">No applications have been received for this position.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="applicationsTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th width="5%">#</th>
                                        <th width="15%">Application No.</th>
                                        <th width="20%">Applicant Name</th>
                                        <th width="12%">Position Ref</th>
                                        <th width="18%">Position Title</th>
                                        <th width="15%">Email</th>
                                        <th width="15%">Applied Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $count = 1; ?>
                                    <?php foreach ($applications as $application): ?>
                                        <tr>
                                            <td><?= $count++ ?></td>
                                            <td>
                                                <strong><?= esc($application['application_number']) ?></strong>
                                            </td>
                                            <td>
                                                <strong><?= esc($application['full_name']) ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary"><?= esc($application['position_reference']) ?></span>
                                            </td>
                                            <td>
                                                <small><?= esc($application['position_title']) ?></small>
                                            </td>
                                            <td>
                                                <small><?= esc($application['email_address']) ?></small>
                                            </td>
                                            <td>
                                                <small>
                                                    <?= date('M d, Y', strtotime($application['created_at'])) ?>
                                                    <br>
                                                    <span class="text-muted">
                                                        <?= date('H:i', strtotime($application['created_at'])) ?>
                                                    </span>
                                                </small>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Information Card -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-body">
                    <h6 class="card-title text-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Information
                    </h6>
                    <p class="card-text">
                        This report shows all applications received for the position "<?= esc($position['designation']) ?>". 
                        The data includes applicant details, application numbers, and submission dates.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#applicationsTable').DataTable({
        responsive: true,
        pageLength: 25,
        order: [[6, 'desc']], // Sort by applied date (newest first)
        columnDefs: [
            { orderable: false, targets: [0] } // Disable sorting for # column
        ]
    });
});

function exportApplicationRegisterByPosition() {
    const button = event.target.closest('button');
    const originalText = button.innerHTML;

    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Exporting...';
    button.disabled = true;

    // Get position ID from URL or data
    const positionId = <?= $position['id'] ?>;
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';

    // Create temporary form for direct download
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '<?= base_url('reports/application-register-position/export') ?>';
    form.target = '_blank'; // Open in new tab
    form.style.display = 'none';

    // Add position ID
    const positionInput = document.createElement('input');
    positionInput.type = 'hidden';
    positionInput.name = 'position_id';
    positionInput.value = positionId;
    form.appendChild(positionInput);

    // Add CSRF token if available
    if (csrfToken) {
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token_name';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);
    }

    // Submit form and cleanup
    document.body.appendChild(form);
    form.submit();

    setTimeout(() => {
        document.body.removeChild(form);
        button.innerHTML = originalText;
        button.disabled = false;
    }, 1000);
}
</script>
<?= $this->endSection() ?>
