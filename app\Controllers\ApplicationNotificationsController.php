<?php

namespace App\Controllers;

use App\Models\ExerciseModel;
use App\Models\PositionsModel;
use App\Models\PositionsGroupModel;
use App\Models\AppxApplicationDetailsModel;
use App\Models\ApplicantsModel;
use App\Models\UsersModel;
use App\Services\EmailService;

class ApplicationNotificationsController extends BaseController
{
    protected $exerciseModel;
    protected $positionsModel;
    protected $positionGroupModel;
    protected $applicationModel;
    protected $applicantsModel;
    protected $usersModel;
    protected $emailService;

    public function __construct()
    {
        $this->exerciseModel = new ExerciseModel();
        $this->positionsModel = new PositionsModel();
        $this->positionGroupModel = new PositionsGroupModel();
        $this->applicationModel = new AppxApplicationDetailsModel();
        $this->applicantsModel = new ApplicantsModel();
        $this->usersModel = new UsersModel();
        $this->emailService = new EmailService();
    }

    /**
     * List exercises for notifications (Dashboard -> Exercises)
     */
    public function index()
    {
        // Get organization ID from session
        $orgId = session()->get('org_id');
        if (!$orgId) {
            session()->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        // Get exercises with status = 'selection' for this organization with notification counts
        $exercises = $this->exerciseModel->select('
                exercises.*,
                COUNT(DISTINCT appx_application_details.id) as total_applications,
                SUM(CASE WHEN appx_application_details.shortlist_status IN ("shortlisted", "eliminated")
                    AND (appx_application_details.shortlisting_notified_at IS NULL
                         OR appx_application_details.shortlisting_notified_at = ""
                         OR appx_application_details.shortlisting_notified_status IS NULL
                         OR appx_application_details.shortlisting_notified_status = ""
                         OR appx_application_details.shortlisting_notified_by IS NULL
                         OR appx_application_details.shortlisting_notified_by = "") THEN 1 ELSE 0 END) as notifications_not_sent,
                SUM(CASE WHEN appx_application_details.shortlisting_notified_at IS NOT NULL
                         AND appx_application_details.shortlisting_notified_at != ""
                         AND appx_application_details.shortlisting_notified_status IS NOT NULL
                         AND appx_application_details.shortlisting_notified_status != ""
                         AND appx_application_details.shortlisting_notified_by IS NOT NULL
                         AND appx_application_details.shortlisting_notified_by != "" THEN 1 ELSE 0 END) as notifications_sent
            ')
            ->join('positions_groups', 'exercises.id = positions_groups.exercise_id', 'left')
            ->join('positions', 'positions_groups.id = positions.position_group_id', 'left')
            ->join('appx_application_details', 'positions.id = appx_application_details.position_id', 'left')
            ->where('exercises.status', 'selection')
            ->where('exercises.org_id', $orgId)
            ->groupBy('exercises.id')
            ->orderBy('exercises.created_at', 'DESC')
            ->findAll();

        $data = [
            'title' => 'Applications Notifications - Select Exercise',
            'menu' => 'notifications',
            'exercises' => $exercises
        ];

        return view('application_notifications/application_notifications_exercises', $data);
    }

    /**
     * List position groups for an exercise (Exercises -> Position Groups)
     */
    public function positionGroups($exerciseId)
    {
        // Get exercise details
        $exercise = $this->exerciseModel->select('
                exercises.id,
                exercises.exercise_name,
                exercises.advertisement_no,
                dakoii_org.org_name
            ')
            ->join('dakoii_org', 'exercises.org_id = dakoii_org.id', 'left')
            ->where('exercises.id', $exerciseId)
            ->first();

        if (!$exercise) {
            return redirect()->to('/applications/notifications')->with('error', 'Exercise not found');
        }

        // Get position groups for this exercise with notification counts
        $positionGroups = $this->positionGroupModel->select('
                positions_groups.*,
                COUNT(DISTINCT positions.id) as position_count,
                COUNT(DISTINCT appx_application_details.id) as total_applications,
                SUM(CASE WHEN appx_application_details.shortlist_status IN ("shortlisted", "eliminated")
                    AND (appx_application_details.shortlisting_notified_at IS NULL
                         OR appx_application_details.shortlisting_notified_at = ""
                         OR appx_application_details.shortlisting_notified_status IS NULL
                         OR appx_application_details.shortlisting_notified_status = ""
                         OR appx_application_details.shortlisting_notified_by IS NULL
                         OR appx_application_details.shortlisting_notified_by = "") THEN 1 ELSE 0 END) as notifications_not_sent,
                SUM(CASE WHEN appx_application_details.shortlisting_notified_at IS NOT NULL
                         AND appx_application_details.shortlisting_notified_at != ""
                         AND appx_application_details.shortlisting_notified_status IS NOT NULL
                         AND appx_application_details.shortlisting_notified_status != ""
                         AND appx_application_details.shortlisting_notified_by IS NOT NULL
                         AND appx_application_details.shortlisting_notified_by != "" THEN 1 ELSE 0 END) as notifications_sent
            ')
            ->join('positions', 'positions_groups.id = positions.position_group_id', 'left')
            ->join('appx_application_details', 'positions.id = appx_application_details.position_id', 'left')
            ->where('positions_groups.exercise_id', $exerciseId)
            ->groupBy('positions_groups.id')
            ->orderBy('positions_groups.group_name', 'ASC')
            ->findAll();

        $data = [
            'title' => 'Applications Notifications - Select Position Group',
            'menu' => 'notifications',
            'exercise' => $exercise,
            'positionGroups' => $positionGroups
        ];

        return view('application_notifications/application_notifications_position_groups', $data);
    }

    /**
     * List positions for a position group (Position Groups -> Positions)
     */
    public function positions($positionGroupId)
    {
        // Get position group details
        $positionGroup = $this->positionGroupModel->select('
                positions_groups.id,
                positions_groups.group_name,
                positions_groups.exercise_id,
                exercises.exercise_name,
                exercises.advertisement_no,
                dakoii_org.org_name
            ')
            ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
            ->join('dakoii_org', 'positions_groups.org_id = dakoii_org.id', 'left')
            ->where('positions_groups.id', $positionGroupId)
            ->first();

        if (!$positionGroup) {
            return redirect()->to('/applications/notifications')->with('error', 'Position group not found');
        }

        // Get positions in this group with notification counts
        $positions = $this->positionsModel->select('
                positions.id,
                positions.designation,
                positions.classification,
                positions.location,
                COUNT(DISTINCT appx_application_details.id) as total_applications,
                SUM(CASE WHEN appx_application_details.shortlist_status = "shortlisted" THEN 1 ELSE 0 END) as shortlisted_count,
                SUM(CASE WHEN appx_application_details.shortlist_status = "eliminated" THEN 1 ELSE 0 END) as eliminated_count,
                SUM(CASE WHEN appx_application_details.shortlist_status IN ("shortlisted", "eliminated")
                    AND appx_application_details.shortlisting_notified_at IS NULL THEN 1 ELSE 0 END) as notifications_not_sent,
                SUM(CASE WHEN appx_application_details.shortlisting_notified_at IS NOT NULL THEN 1 ELSE 0 END) as notifications_sent
            ')
            ->join('appx_application_details', 'positions.id = appx_application_details.position_id', 'left')
            ->where('positions.position_group_id', $positionGroupId)
            ->groupBy('positions.id')
            ->orderBy('positions.designation', 'ASC')
            ->findAll();

        $data = [
            'title' => 'Applications Notifications - Select Position',
            'menu' => 'notifications',
            'positionGroup' => $positionGroup,
            'positions' => $positions
        ];

        return view('application_notifications/application_notifications_positions', $data);
    }

    /**
     * List applications for a position (Positions -> Applications) - Show shortlisted/eliminated applications
     */
    public function applications($positionId)
    {
        // Get position details with related information
        $position = $this->positionsModel->select('
                positions.*,
                positions_groups.id as position_group_id,
                positions_groups.group_name,
                positions_groups.exercise_id,
                exercises.exercise_name,
                exercises.advertisement_no,
                exercises.gazzetted_no,
                exercises.gazzetted_date,
                dakoii_org.org_name
            ')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
            ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
            ->where('positions.id', $positionId)
            ->first();

        if (!$position) {
            return redirect()->to('/applications/notifications')->with('error', 'Position not found');
        }

        // Get applications for this position that have been shortlisted or eliminated
        $applications = $this->applicationModel->select('
                appx_application_details.*,
                positions.designation as position_title,
                users.name as notified_by_name
            ')
            ->join('positions', 'appx_application_details.position_id = positions.id', 'left')
            ->join('users', 'appx_application_details.shortlisting_notified_by = users.id', 'left')
            ->where('appx_application_details.position_id', $positionId)
            ->whereIn('appx_application_details.shortlist_status', ['shortlisted', 'eliminated'])
            ->orderBy('appx_application_details.shortlist_status', 'DESC') // shortlisted first
            ->orderBy('appx_application_details.rating_capability_max', 'DESC')
            ->findAll();

        $data = [
            'title' => 'Applications Notifications - Send Notifications',
            'menu' => 'notifications',
            'position' => $position,
            'applications' => $applications
        ];

        return view('application_notifications/application_notifications_applications', $data);
    }

    /**
     * Send successful shortlisting notification (POST method)
     */
    public function sendSuccessfulNotification($applicationId)
    {
        // Get application details with related data
        $application = $this->applicationModel->getApplicationWithDetails($applicationId);
        
        if (!$application) {
            return redirect()->back()->with('error', 'Application not found');
        }

        // Check if application is shortlisted
        if ($application['shortlist_status'] !== 'shortlisted') {
            return redirect()->back()->with('error', 'Application is not shortlisted');
        }

        // Check if notification already sent
        if (!empty($application['shortlisting_notified_at'])) {
            return redirect()->back()->with('warning', 'Notification already sent for this application');
        }

        // Get applicant email from applicants table
        $applicant = $this->applicantsModel->find($application['applicant_id']);
        if (!$applicant || empty($applicant['email'])) {
            return redirect()->back()->with('error', 'Applicant email not found');
        }

        // Send successful notification email
        $emailResult = $this->emailService->sendSuccessfulShortlistingNotification($application, $applicant['email']);

        if ($emailResult['success']) {
            // Update notification fields
            $this->applicationModel->update($applicationId, [
                'shortlisting_notified_at' => date('Y-m-d H:i:s'),
                'shortlisting_notified_status' => 'successful',
                'shortlisting_notified_by' => session()->get('user_id')
            ]);

            return redirect()->back()->with('success', 'Successful shortlisting notification sent to ' . $applicant['email']);
        } else {
            return redirect()->back()->with('error', 'Failed to send notification: ' . $emailResult['message']);
        }
    }

    /**
     * Send unsuccessful shortlisting notification (POST method)
     */
    public function sendUnsuccessfulNotification($applicationId)
    {
        // Get application details with related data
        $application = $this->applicationModel->getApplicationWithDetails($applicationId);
        
        if (!$application) {
            return redirect()->back()->with('error', 'Application not found');
        }

        // Check if application is eliminated
        if ($application['shortlist_status'] !== 'eliminated') {
            return redirect()->back()->with('error', 'Application is not eliminated');
        }

        // Check if notification already sent
        if (!empty($application['shortlisting_notified_at'])) {
            return redirect()->back()->with('warning', 'Notification already sent for this application');
        }

        // Get applicant email from applicants table
        $applicant = $this->applicantsModel->find($application['applicant_id']);
        if (!$applicant || empty($applicant['email'])) {
            return redirect()->back()->with('error', 'Applicant email not found');
        }

        // Send unsuccessful notification email
        $emailResult = $this->emailService->sendUnsuccessfulShortlistingNotification($application, $applicant['email']);

        if ($emailResult['success']) {
            // Update notification fields
            $this->applicationModel->update($applicationId, [
                'shortlisting_notified_at' => date('Y-m-d H:i:s'),
                'shortlisting_notified_status' => 'unsuccessful',
                'shortlisting_notified_by' => session()->get('user_id')
            ]);

            return redirect()->back()->with('success', 'Unsuccessful shortlisting notification sent to ' . $applicant['email']);
        } else {
            return redirect()->back()->with('error', 'Failed to send notification: ' . $emailResult['message']);
        }
    }
}
